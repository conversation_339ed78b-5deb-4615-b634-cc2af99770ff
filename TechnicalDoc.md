# Profolify: Technical Reference Document

**Version:** 3.0
**Status:** Updated with Database Restructuring & User Management
**Last Updated:** July 2025

## 1. Executive Summary

This document provides a definitive technical deep-dive into the Profolify platform. It is intended for developers to understand not only *how* the system works, but *why* specific architectural decisions were made. It covers the final, production-ready implementation and explicitly details the evolution of key systems, including previously failed approaches, to serve as a complete engineering record.

**Recent Major Updates (v3.0):**
- **Database Restructuring**: Implemented separate `users` and `portfolios` collections for multi-tenancy
- **User Management System**: Complete user lifecycle management with migration support
- **Authentication Overhaul**: Streamlined Google-only authentication with improved user experience
- **Premium Features Foundation**: Built infrastructure for future premium subscription model
- **Migration System**: Automated migration for existing users to new database structure

**Core Technical Achievements:**

-   **Live DOM Capture Export:** A revolutionary, client-side export system that produces pixel-perfect static sites from the live, rendered DOM, ensuring 100% fidelity and theme independence.
-   **Dual-Mode Theming Engine:** A highly scalable theme architecture where a single set of React components can operate in both a dynamic, inline editor and a static, public-facing mode.
-   **Layered State Management:** A clear and deliberate separation of state concerns (server, editor, global) that simplifies development and enhances performance.

---

## 2. System Architecture & Technology Stack

Profolify is a modern **Jamstack** application. The frontend (Next.js) is decoupled from the backend services (Firebase, Cloudinary), enabling high performance and scalability.

| Technology        | Role                  | Rationale                                                                                                                            |
| :---------------- | :-------------------- | :----------------------------------------------------------------------------------------------------------------------------------- |
| **Next.js 15**      | Framework             | App Router provides a hybrid rendering model perfect for a dynamic editor (Client Components) and fast public pages (Server Components). |
| **Firebase Auth**   | Authentication        | Secure, scalable, and easy to integrate for Google Sign-In. Handles all session management.                                          |
| **Firestore**       | Database              | A NoSQL, document-based database that is flexible, scales automatically, and provides real-time data synchronization.                 |
| **Cloudinary**      | File Storage          | A specialized media platform that handles image optimization, transformations, and provides a fast CDN, offloading media tasks.     |
| **TanStack Query**  | Server State          | Manages all async operations (fetching/updating data). It handles caching and loading/error states, simplifying component logic. |
| **React Context**   | Editor State          | The `EditorContext` uses `useReducer` to manage the complex, nested state of the portfolio editor, ensuring predictable state updates. |
| **Zustand**         | Global UI State       | A lightweight solution for simple global state, primarily for tracking the current user's authentication status across the app.       |
| **Shadcn/UI & TW**  | UI & Styling          | A modern, utility-first approach to styling with a set of high-quality, accessible components.                                     |

---

## 3. Database Restructuring & User Management (v3.0)

### 3.1 Database Architecture Evolution

**Previous Structure (v2.0):**
```
portfolios/
  └── {userId}/
      ├── id: userId
      ├── userName: string
      ├── profession: string
      ├── projects: Project[]
      └── ... (all portfolio data)
```

**New Structure (v3.0):**
```
users/
  └── {userId}/
      ├── uid: string
      ├── email: string
      ├── displayName: string
      ├── photoURL?: string
      ├── plan: 'free' | 'premium'
      ├── createdAt: Date
      ├── updatedAt: Date
      ├── premiumExpiresAt?: Date
      └── settings: UserSettings

portfolios/
  └── {portfolioId}/
      ├── id: portfolioId
      ├── userId: string (reference to users collection)
      ├── slug: string
      ├── templateId: string
      ├── isPublished: boolean
      ├── projects: Project[]
      └── ... (all portfolio content)
```

### 3.2 Migration System Implementation

**Migration Strategy:**
- **Backward Compatibility**: Old users without user records are automatically migrated
- **Zero Downtime**: Migration happens seamlessly during authentication
- **Data Integrity**: All existing portfolio data is preserved

**Key Migration Components:**

1. **`lib/user-api.ts`** - User management and migration functions
2. **`lib/auth-middleware.ts`** - Authentication flow with migration support
3. **`app/api/migrate-slugs/route.ts`** - API endpoint for slug migration
4. **`stores/auth-store.ts`** - Enhanced auth state management

### 3.3 User Management System Features

**Complete User Lifecycle Management:**
- User creation during Google Auth
- Profile management and settings
- Premium subscription infrastructure
- Account deletion with cascade cleanup

**Updated Firestore Security Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Portfolios collection
    match /portfolios/{portfolioId} {
      allow get: if resource.data.isPublished == true ||
                    (request.auth != null && request.auth.uid == resource.data.userId);
      allow list: if true; // Safe with query filters
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow update, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

### 3.4 Authentication Overhaul

**Simplified Authentication Flow:**
- Google-only authentication for streamlined UX
- Improved loading states and error handling
- Enhanced user data management
- Automatic migration for existing users

---

## 4. Recent Bug Fixes & Solutions (July 2025)

### 4.1 Export Race Condition & Incomplete HTML

**Issue:** The live DOM capture system, which uses a hidden `iframe` to render the portfolio, sometimes produced an empty or incomplete `index.html` file. This was caused by a race condition where the HTML was captured before the page had fully hydrated and rendered its content.

**Solution:** The `setTimeout` delay in the `captureLivePortfolioDOM` function within `lib/live-dom-capture.ts` was increased from `2000ms` to `5000ms`. This provides a more generous window for the page to fully load, resolving the race condition and ensuring the exported HTML is always complete.

```typescript
// In lib/live-dom-capture.ts
iframe.onload = () => {
  // ...
  setTimeout(() => {
    // ... DOM capture logic
  }, 5000); // Wait 5 seconds for content to load
};
```

### 4.2 Scalability of Theme Navbar CSS

**Issue:** The global CSS file contained specific overrides for each theme's navbar (e.g., `.theme-modern-navbar`, `.theme-creative-navbar`) to control its behavior in the editor. This was not scalable, as each new theme would require another entry in the global stylesheet.

**Solution:** A generic `theme-navbar` class was introduced. Each theme's navbar component now includes this class. The global CSS was refactored to use a single, generic selector (`.portfolio-theme-container .theme-navbar`), making the system scalable and easier to maintain.



### 4.1 Text Handling in Editable Components

**Issue:** Text pasted into editable fields was including HTML formatting which caused display issues.

**Solution:** Modified the `handlePaste` function in the `EditableText` component to strip HTML from pasted content:

```typescript
const handlePaste = (e: React.ClipboardEvent) => {
  e.preventDefault();
  const text = e.clipboardData.getData('text/plain');
  document.execCommand('insertText', false, text);
};
```

### 4.2 Text Overflow Issues in Modern Theme

#### Hero Section Role Field
**Issue:** Long role/profession text was getting cut off in published mode.

**Solution:** Added text wrapping properties:
```css
.theme-modern-hero-subtitle {
  word-break: break-word;
  overflow-wrap: break-word;
}
```

#### Project Section
**Project Title:**
```css
.modern-project-title {
  word-break: break-word;
  overflow-wrap: break-word;
}
```

**Project Description:**
```css
.modern-project-description-text {
  word-break: break-word;
  overflow-wrap: break-word;
}
```

#### Skills Section
**Issue:** Long skill names were overflowing their containers.

**Solution:**
```css
.modern-skill-badge-text {
  word-break: break-word;
  overflow-wrap: break-word;
}
```

### 3.3 Footer "Powered by" Section in Exports

**Issue:** The "Powered by Profolify" section was visible in the exported static site.

**Solution:** Used CSS to hide the section when the `data-export` attribute is present:

```css
[data-export] .theme-modern-footer-attribution {
  display: none !important;
}
```

## 5. The Live DOM Capture Export System

This is the platform's most innovative feature. Its implementation was an evolutionary process that moved from fragile, template-based methods to a robust, theme-agnostic final architecture.

### 5.1. The Engineering Journey: From Failure to Success

1.  **❌ Attempt 1: Template-Based Export (Abandoned):** The initial concept involved creating a separate HTML template for each theme and populating it with data. This was quickly abandoned as it was unscalable, brittle, and defeated the purpose of a modular theme system by forcing all themes to conform to a single, rigid data structure.

2.  **❌ Attempt 2: Server-Side Rendering (Failed):** A Node.js-based API route (`/api/create-export-zip`) was created to render React components to a string on the server. This approach failed due to insurmountable complexities with module resolution in a serverless environment and frequent deployment timeouts on Vercel.

3.  **✅ Attempt 3: Live DOM Capture (Success):** The breakthrough was to shift the entire process to the client-side and capture the portfolio *as the browser has already rendered it*. This approach is resilient, scalable, and guarantees 100% fidelity.

### 5.2. Final Technical Architecture: `lib/live-dom-capture.ts`

The process is orchestrated entirely on the client:

1.  **Initiation & Hidden Iframe:** On export, a hidden `<iframe>` is dynamically created and its `src` is set to the user's public portfolio URL.

2.  **DOM Cloning:** Once the `iframe`'s `onload` event fires, the script reaches into the iframe's content, finds the theme's root element (`[data-theme-root]`), and performs a `cloneNode(true)`. This creates an in-memory copy of the fully rendered, styled DOM.

3.  **HTML Sanitization:** The cloned DOM node is meticulously cleaned. All editor-specific artifacts (`contenteditable` attributes, `data-editor-*` tags, etc.) are stripped out.

4.  **Multi-Layer Image Fixing:** This is the most critical step. A series of functions are run to convert all Next.js-optimized image URLs (`/_next/image?url=...`) back to their original, portable Cloudinary URLs. This is done via multiple layers of defense: direct component logic during an 'export render', DOM node manipulation, and finally, regex-based string replacement on the final HTML string to catch any edge cases.

5.  **CSS & JS Injection:** The theme's compiled CSS is fetched and injected into a `<style>` tag. A small, dependency-free JavaScript snippet is also injected to power interactivity like mobile menus.

6.  **Packaging:** The final, clean HTML string is passed to `JSZip`, which creates a `.zip` file in the browser's memory, and the user is prompted to download it.

This client-side architecture is infinitely scalable as it places zero computational load on the server.

### 5.3. Deep Dive: The 6-Layer Image Fixing System

Ensuring images work in a static export was the biggest challenge. The final solution is a robust, multi-layered defense:

```typescript
// Layer 1: Export Context Hook
// A reliable way for any component to know it's being rendered for export.
export function useIsExport(): boolean {
    const context = useContext(ExportContext);
    const globalFlag = (window as any).__PORTFOLIO_EXPORT__;
    return context?.isExport || !!globalFlag;
}

// Layer 2: The `PortfolioImage` Component
// This component uses the hook to decide whether to render a Next.js `<Image>`
// or a standard `<img>` tag with the direct Cloudinary URL.
if (isExport) {
    return <img src={props.src} alt={props.alt} ... />;
}
return <Image src={props.src} alt={props.alt} ... />;

// Layers 3 & 4: DOM & HTML String Fixing
// After cloning the DOM, we run two passes to catch any `<img>` tags that
// were not handled by Layer 2.
fixNextJSImagesInDOM(clonedElement); // Pass 1: Manipulate the DOM nodes directly
let htmlString = clonedElement.outerHTML;
htmlString = fixNextJSImageURLsInHTML(htmlString); // Pass 2: Use regex on the string

// Layers 5 & 6: Aggressive Fallback & Verification
// Final, aggressive regex passes and verification steps ensure that absolutely
// no `/_next/image` URLs remain in the final output.
```

---

## 4. The Inline Editor: State Management & UX Challenges

### 4.1. The `EditableText` Component: An Engineering Journey

The inline text editing component is a prime example of the project's iterative engineering process.

-   **The Challenge:** A `contenteditable` `<div>` provides a seamless editing experience, but it creates a conflict between the browser's direct DOM manipulation and React's Virtual DOM. An early, naive implementation that tied the `<div>` directly to the global `EditorContext` state resulted in a jarring UX: the input would flicker and lose focus as unrelated state changes (like an image upload completing) caused the component to re-render.

-   **Attempted Solution & New Problems:** The logical next step was to give `EditableText` its own internal state, only calling out to the global context `onBlur`. While this fixed the flickering, it introduced a worse set of UX failures:
    1.  **Data Loss:** If a user edited text and then clicked "Publish" or navigated away without first blurring the input, their latest changes were not in the global state and were therefore lost.
    2.  **Stale UI:** The "Publish" button's disabled state (which depends on a `isDirty` flag) would not update in real-time as the user typed.

-   **Final, Accepted Solution:** The final implementation reverts to a **fully controlled component** tied directly to the `EditorContext`. The auto-save mechanism in `app/(private)/portfolio/page.tsx` was enhanced with a more aggressive `debounce` from the `use-debounce` library.

    ```tsx
    // In app/(private)/portfolio/page.tsx
    const [debouncedState] = useDebouncedValue(state.formData, 1000);

    useEffect(() => {
        if (isDirty) {
            autoSaveMutation.mutate(debouncedState);
        }
    }, [debouncedState]);
    ```

    While a very minor flicker can theoretically still occur in extreme race conditions, this approach was deemed superior because it **guarantees data integrity and a predictable UI**, which were higher priorities than eliminating a rare visual artifact.

---

## 6.0 Scalability & Business Strategy

### 6.1 Current Architecture's Scalability

The current architecture is built to scale:
*   **Themes:** Adding new themes is simple. You create new theme and section components, add the template to the theme registry, and the system automatically supports it for editing, publishing, and exporting.
*   **Features:** The separation of concerns makes it easy to add new features. For example, adding an "Analytics" page would be a self-contained task that doesn't interfere with the editor.
*   **Backend:** Firebase and Vercel are serverless platforms that scale automatically to handle traffic spikes without manual intervention.

### 6.2 Monetization Strategy: The Freemium Model

The features you have built create a natural path to monetization.

*   **Free Tier (`Profolify Free`):**
    *   Access to all themes and the portfolio editor.
    *   Publish to a `profolify.app/your-slug` subdomain.
    *   Includes the **Static Site Export** feature. This is a powerful offering for free users, especially developers.

*   **Paid Tier (`Profolify Pro`):**
    *   Everything in the Free plan.
    *   **Premium Feature: Custom Domains.** The ability to connect `www.your-domain.com` to a Profolify portfolio with live updates. This is a high-value feature that users will pay a monthly/annual subscription for.
    *   Potentially other features like advanced analytics, more themes, or password-protected portfolios.

### 6.3 Future Feature Roadmap

The current foundation allows for many exciting future enhancements:
1.  **Custom Domain Mapping:** Implement the Vercel API integration to allow users to connect their own domains.
2.  **Portfolio Analytics:** Track page views for published portfolios and display the data in a new "Analytics" tab on the dashboard.
3.  **More Themes:** Design and build out a wider variety of templates to appeal to different professions (photographers, writers, etc.).
4.  **Draggable Sections:** Re-introduce a more stable drag-and-drop system for reordering portfolio sections.
5.  **AI-Powered Content Suggestions:** Integrate an AI tool to help users write compelling descriptions for their projects and "About Me" sections.

---

## 7. Universal CSS Compilation System for Export Compatibility

**The Challenge: Modular CSS vs Static Export**

Profolify uses a modular CSS architecture for development efficiency, but this creates challenges for static exports:

- **Development**: Individual component CSS files for easy maintenance
- **Export**: Static sites need single CSS files without external dependencies

**The Solution: Universal Compiler Architecture**

We implemented a universal CSS compilation system that automatically handles all themes:

```typescript
// Component CSS Files (source)
themes/creative-minimalist/components/navbar.css
themes/creative-minimalist/components/hero.css
themes/creative-minimalist/components/about.css
// ... other components

// Compiled CSS (auto-generated)
themes/creative-minimalist/creative-minimalist-compiled.css
```

**Universal CSS Compilation Process:**

1. **Component CSS Files**: Individual CSS files for each theme section
   ```
   themes/[theme-id]/components/
   ├── navbar.css
   ├── hero.css
   ├── experience.css
   ├── skills.css
   └── ...
   ```

2. **Universal Compiler**: `scripts/compile-themes.js`
   - Auto-detects all themes with modular structure
   - Combines all component styles into single compiled files
   - Works with unlimited themes without configuration

3. **Automatic Integration**:
   ```bash
   npm run sync-themes  # Universal compiler: compiles ALL themes + syncs to public/
   npm run build        # Compiles all themes + builds for production
   ```

**Technical Implementation:**

```javascript
// Universal CSS Compilation Process
function compileAllThemes() {
  // 1. Auto-detect all themes with modular structure
  const themes = detectModularThemes();

  // 2. Process each theme
  for (const theme of themes) {
    // 3. Read all component CSS files
    for (const componentFile of theme.componentFiles) {
      const componentContent = fs.readFileSync(componentFile, 'utf8');
      compiledContent += componentContent;
    }

    // 4. Write compiled CSS file
    fs.writeFileSync(theme.compiledFile, compiledContent);
  }
}
```

**Benefits:**
- ✅ **Development Efficiency**: Edit individual component CSS files
- ✅ **Export Reliability**: Single CSS file with no external dependencies
- ✅ **Automatic Process**: Compilation happens during build/sync
- ✅ **Consistent Styling**: Same styles in live and exported sites

#### **4.3.7 Implementation Lessons Learned**

**Key Insights from Development:**

1. **Simplicity Wins:** The complex server-side approach was over-engineered. The simple client-side solution is more reliable and maintainable.

2. **CSS Strategy is Critical:** The biggest challenge was ensuring CSS loads correctly across all modes. Our unified CSS architecture solution:
   - **Development**: Uses compiled CSS files imported in layout.tsx for consistency
   - **Live Site**: Same compiled CSS files ensure identical styling
   - **Export**: Same compiled CSS files fetched via URLs for static site generation
   - **Compilation**: Universal compiler automatically handles all themes
   - **Consistency**: All modes use identical CSS for perfect fidelity

3. **HTML-CSS Alignment:** Generated HTML must use exact CSS classes from theme files. Any mismatch results in unstyled exports.

4. **Build System Simplification:** Removing complex build steps eliminated most deployment and development issues.

5. **Client-Side Benefits:** Browser-based export generation provides better debugging, no server timeouts, and instant feedback.

**Code Quality Improvements:**
- Removed ~20+ unnecessary files and configurations
- Eliminated 3 unused npm dependencies
- Simplified build process from multi-step to single command
- Reduced bundle size and build time
- Improved developer experience with cleaner error messages

**Production Validation:**
- ✅ Export works in development (`npm run dev`)
- ✅ Export works in production build (`npm run build`)
- ✅ Exported sites match live versions pixel-perfectly
- ✅ Mobile responsiveness maintained in exports
- ✅ Interactive elements (menus) work offline
- ✅ No external dependencies except Alpine.js CDN

---

## **5.0 Scalability & Business Strategy**

This completes the comprehensive technical documentation for Profolify v3.0, covering all major architectural changes, database restructuring, and system improvements implemented in the latest version.
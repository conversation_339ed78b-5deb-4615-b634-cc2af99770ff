"use client";

import { useAuth } from "@/contexts/AuthenticationContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import React, { Suspense } from "react";
import { PortfolioData } from "@/lib/types";
import { getUserPortfolios, deletePortfolio } from '@/lib/portfolio-api';
import { useUniversalExport } from "@/hooks/useUniversalExport";
import { ConfirmationDialog } from '@/components/custom-ui/ConfirmationDialog';
import { useLogout } from "@/hooks/use-logout";
import { toast } from "sonner";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { FullScreenLoader } from "@/components/custom-ui/FullScreenLoader";
import DashboardHeader from "@/components/dashboard-page/DashboardHeader";
import WelcomeSection from "@/components/dashboard-page/WelcomeSection";
import ThemeSelector from "@/components/dashboard-page/ThemeSelector";

const PortfolioStatusCard = React.lazy(() => import("@/components/dashboard-page/PortfolioStatusCard"));

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { exportPortfolio, isExporting } = useUniversalExport();
  const logout = useLogout();
  const [isNavigating] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const { data: portfolios, isLoading } = useQuery<PortfolioData[]>({
    queryKey: ["portfolios", user?.uid],
    queryFn: () => getUserPortfolios(user!.uid),
    enabled: !!user,
  });

  // Get the first portfolio (for now, we'll show the first one)
  const portfolio = portfolios?.[0] || null;

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };

  // Template selection handler - passed to ThemeSelector component
  const handleSelectTemplate = () => {
    // This will be handled by the ThemeSelector component
  };


  const handleChangeTheme = () => {
    router.push('/dashboard/themes');
  };

  const deleteMutation = useMutation({
    mutationFn: () => {
      if (!portfolio?.id) throw new Error('Portfolio ID not found');
      return deletePortfolio(portfolio.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success('Portfolio deleted successfully.');
    },
    onError: (error) => {
      toast.error(`Failed to delete portfolio: ${error.message}`);
    },
  });

  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => {} });

  const handleDeletePortfolio = () => {
    setDialog({ isOpen: true, onConfirm: () => deleteMutation.mutate() });
  };

  const handleExport = async () => {
    if (!portfolio) {
      toast.error("No portfolio data available for export.");
      return;
    }

    try {
      await exportPortfolio(portfolio);
      toast.success("Export successful! Check your downloads.");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Export failed: ${error.message}`);
      } else {
        toast.error("Export failed: An unknown error occurred.");
      }
    }
  };

  if (loading || isLoading || isNavigating || isSigningOut) {
    return (
      <FullScreenLoader
        text={
          isNavigating
            ? "Setting up your portfolio editor..."
            : isSigningOut
            ? "Signing out..."
            : "Loading your dashboard..."
        }
      />
    );
  }

  return (
    <div className="min-h-screen">
      {/* Navigation Header */}
      <DashboardHeader
        user={user}
        onSignOut={handleSignOut}
        isSigningOut={isSigningOut}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <WelcomeSection user={user} hasPortfolio={!!portfolio} />

          {/* Portfolio Status Card - Only show if user has a portfolio */}
          {portfolio && (
            <Suspense fallback={<div className="flex justify-center items-center py-8"><Loader2 className="h-8 w-8 animate-spin text-brandPrimary" /></div>}>
              <PortfolioStatusCard
                portfolio={portfolio}
                onExport={handleExport}
                isExporting={isExporting}
                onChangeTheme={handleChangeTheme}
                onDelete={handleDeletePortfolio}
              />
            </Suspense>
          )}

          {/* Theme Selector - Always show */}
          <ThemeSelector
            user={user}
            portfolio={portfolio}
            onTemplateSelect={handleSelectTemplate}
          />
        </div>
      </main>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => {} })}
        onConfirm={() => {
          dialog.onConfirm();
          setDialog({ isOpen: false, onConfirm: () => {} });
        }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
        isDestructive
        isPending={deleteMutation.isPending}
      />
    </div>
  );
}

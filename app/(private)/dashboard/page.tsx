"use client";

import { useAuth } from "@/contexts/AuthenticationContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Loader2, LogOut, Sparkles, Check, Download, Palette, Settings } from "lucide-react";
import React, { Suspense } from "react";
import { PortfolioData } from "@/lib/types";
import { getUserPortfolios, createPortfolioFromTemplate, deletePortfolio } from '@/lib/portfolio-api';
import { useUniversalExport } from "@/hooks/useUniversalExport";
import { ConfirmationDialog } from '@/components/custom-ui/ConfirmationDialog';
import { useLogout } from "@/hooks/use-logout";
import { toast } from "sonner";
import { useState } from "react";
import { getAllThemes } from "@/themes/theme-registry";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { FullScreenLoader } from "@/components/custom-ui/FullScreenLoader";

const PortfolioStatusCard = React.lazy(() => import("@/components/dashboard-page/PortfolioStatusCard"));

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { exportPortfolio, isExporting } = useUniversalExport();
  const logout = useLogout();
  const [isNavigating, setIsNavigating] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const [loadingTemplateId, setLoadingTemplateId] = useState<string | null>(null);

  const { data: portfolios, isLoading } = useQuery<PortfolioData[]>({
    queryKey: ["portfolios", user?.uid],
    queryFn: () => getUserPortfolios(user!.uid),
    enabled: !!user,
  });

  // Get the first portfolio (for now, we'll show the first one)
  const portfolio = portfolios?.[0] || null;

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };

  // Template selection mutation
  const { mutate: selectTemplate } = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingTemplateId(variables.templateId);
    },
    onSuccess: (newPortfolio) => {
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);
      setIsNavigating(true);
      // Navigate immediately with loading state
      router.push('/portfolio');
    },
    onError: () => {
      setIsNavigating(false);
      setLoadingTemplateId(null);
      toast.error("Failed to create portfolio. Please try again.");
    },
    onSettled: () => {
      setLoadingTemplateId(null);
    }
  });

  // Template selection handler
  const handleSelectTemplate = (templateId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a template.");
      return;
    }
    selectTemplate({
      user,
      templateId
    });
  };

  const handlePreview = () => {
    if (!portfolio) return;
    localStorage.setItem('portfolio-preview', JSON.stringify(portfolio));
    window.open('/portfolio-preview', '_blank');
  };

  const handleChangeTheme = () => {
    router.push('/dashboard/themes');
  };

  const deleteMutation = useMutation({
    mutationFn: () => {
      if (!portfolio?.id) throw new Error('Portfolio ID not found');
      return deletePortfolio(portfolio.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success('Portfolio deleted successfully.');
    },
    onError: (error) => {
      toast.error(`Failed to delete portfolio: ${error.message}`);
    },
  });

  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => {} });

  const handleDeletePortfolio = () => {
    setDialog({ isOpen: true, onConfirm: () => deleteMutation.mutate() });
  };

  const handleExport = async () => {
    if (!portfolio) {
      toast.error("No portfolio data available for export.");
      return;
    }

    try {
      await exportPortfolio(portfolio);
      toast.success("Export successful! Check your downloads.");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Export failed: ${error.message}`);
      } else {
        toast.error("Export failed: An unknown error occurred.");
      }
    }
  };

  if (loading || isLoading || isNavigating || isSigningOut) {
    return (
      <FullScreenLoader
        text={
          isNavigating
            ? "Setting up your portfolio editor..."
            : isSigningOut
            ? "Signing out..."
            : "Loading your dashboard..."
        }
      />
    );
  }

  return (
    <div className="min-h-screen">
      {/* Navigation Bar */}
      <nav className="bg-background border-b ">
        <div className="flex items-center justify-between mx-auto px-8 py-4">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            {/* <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div> */}
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
              <Image src="/icon.svg" alt="logo" width={32} height={32} />
              <span className="font-bold text-xl lg:text-2xl gradient-text">
                Profolify
              </span>
            </Link>
          </div>

          {/* User Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-12 w-12 rounded-full p-0 hover:bg-transparent">
                <Avatar className="h-12 w-12 border-2 hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
                  <AvatarFallback>{user?.displayName?.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user?.displayName}</p>
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/settings')}>
                <Settings className="mr-2 h-4 w-4 hover:text-white" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSignOut} disabled={isSigningOut}>
                {isSigningOut ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogOut className="mr-2 h-4 w-4 hover:text-white" />}
                <span>{isSigningOut ? 'Signing out...' : 'Sign out'}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Scenario A: User has an existing portfolio */}
        {portfolio && (
          <div className="space-y-8">
            {/* Enhanced Welcome Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
              <div className="flex items-center space-x-4 mb-4">

                <Avatar className="h-16 w-16 border-2 hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
                  <AvatarFallback>{user?.displayName?.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="text-3xl font-bold text-slate-800">
                    Welcome back, {user?.displayName}!
                  </h2>
                  <p className="text-slate-600 text-lg">
                    Ready to manage your portfolio and showcase your work to the world.
                  </p>
                </div>
              </div>
            </div>

            <Suspense fallback={<div className="flex justify-center items-center py-8"><Loader2 className="h-8 w-8 animate-spin text-brandPrimary" /></div>}>
              <PortfolioStatusCard 
                portfolio={portfolio}
                onPreview={handlePreview}
                onExport={handleExport}
                isExporting={isExporting}
                onChangeTheme={handleChangeTheme}
                onDelete={handleDeletePortfolio}
              />
            </Suspense>
          </div>
        )}

            {/* Scenario B: User has no portfolio */}
            {!portfolio && user && (
              <div className="space-y-8">
                {/* Enhanced Welcome Section for New Users */}
                <div className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 lg:p-12 border border-blue-100/50 text-center overflow-hidden">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-10 left-10 w-20 h-20 bg-brandPrimary rounded-full blur-xl"></div>
                    <div className="absolute bottom-10 right-10 w-32 h-32 bg-brandSecondary rounded-full blur-xl"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-purple-400 rounded-full blur-2xl"></div>
                  </div>

                  <div className="relative z-10">
                    <div className="flex items-center justify-center mb-6">
                      <div className="relative">
                        <Avatar className="h-20 w-20 border-4 border-white shadow-xl ring-4 ring-brandPrimary/20">
                          <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
                          <AvatarFallback className="text-xl font-bold bg-gradient-to-br from-brandPrimary to-brandSecondary text-white">
                            {user?.displayName?.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-2 border-white flex items-center justify-center">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                      </div>
                    </div>

                    <h2 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-4">
                      Welcome to Profolify, {user?.displayName?.split(' ')[0]}!
                    </h2>

                    <p className="text-xl lg:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                      Let&#39;s create your professional portfolio in minutes
                    </p>

                    <div className="inline-flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-gray-700 shadow-lg border border-white/50">
                      <div className="w-8 h-8 bg-gradient-to-r from-brandPrimary to-brandSecondary rounded-full flex items-center justify-center">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                      <span>Choose a template below to get started</span>
                    </div>
                  </div>
                </div>

                {/* Template Selection Grid */}
                <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {getAllThemes().map((template) => {
                    const isLoadingThisTemplate = loadingTemplateId === template.id;
                    return (
                      <div
                        key={template.id}
                        className="group relative bg-white rounded-2xl border border-gray-200 hover:border-brandPrimary/30 hover:shadow-xl transition-all duration-500 overflow-hidden"
                      >
                        {/* Template Preview */}
                        <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                          <Image
                            src={template.preview || '/thumbnails/default-theme.jpg'}
                            alt={`${template.name} template preview`}
                            fill
                            className="object-cover transition-transform duration-700 group-hover:scale-105"
                          />

                          {/* Overlay on hover */}
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />

                          {/* Template Badge */}
                          <div className="absolute top-4 left-4">
                            <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
                              <Palette className="w-3 h-3 text-brandPrimary" />
                              <span className="text-xs font-medium text-gray-700">Template</span>
                            </div>
                          </div>

                          {/* Features Badge */}
                          <div className="absolute top-4 right-4">
                            <div className="bg-green-100/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-1">
                              <Check className="w-3 h-3 text-green-600" />
                              <span className="text-xs font-medium text-green-700">Free</span>
                            </div>
                          </div>
                        </div>

                        {/* Template Info */}
                        <div className="p-6">
                          <div className="mb-4">
                            <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-brandPrimary transition-colors">
                              {template.name}
                            </h3>
                            <p className="text-gray-600 text-sm leading-relaxed">
                              {template.description}
                            </p>
                          </div>

                          {/* Features List */}
                          <div className="mb-6">
                            <div className="flex flex-wrap gap-2">
                              <span className="inline-flex items-center gap-1 bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full">
                                <Check className="w-3 h-3" />
                                Responsive Design
                              </span>
                              <span className="inline-flex items-center gap-1 bg-purple-50 text-purple-700 text-xs px-2 py-1 rounded-full">
                                <Check className="w-3 h-3" />
                                SEO Optimized
                              </span>
                              <span className="inline-flex items-center gap-1 bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full">
                                <Download className="w-3 h-3" />
                                Export Ready
                              </span>
                            </div>
                          </div>

                          {/* Action Button */}
                          <Button
                            onClick={() => handleSelectTemplate(template.id)}
                            disabled={isLoadingThisTemplate}
                            className="w-full bg-gradient-to-r from-brandPrimary to-brandSecondary hover:from-brandPrimary/90 hover:to-brandSecondary/90 text-white font-semibold py-3 rounded-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
                          >
                            {isLoadingThisTemplate ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Setting up your portfolio...
                              </>
                            ) : (
                              <>
                                <Sparkles className="mr-2 h-4 w-4" />
                                Use This Template
                              </>
                            )}
                          </Button>
                        </div>

                        {/* Loading Overlay */}
                        {isLoadingThisTemplate && (
                          <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10">
                            <div className="text-center">
                              <Loader2 className="w-8 h-8 animate-spin text-brandPrimary mx-auto mb-2" />
                              <p className="text-sm font-medium text-gray-700">Creating your portfolio...</p>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
      </main>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => {} })}
        onConfirm={() => {
          dialog.onConfirm();
          setDialog({ isOpen: false, onConfirm: () => {} });
        }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
        isDestructive
        isPending={deleteMutation.isPending}
      />
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { getUser, deleteUserAccount } from '@/lib/user-api';
import { User } from '@/lib/types';
import { toast } from 'sonner';
import { Crown, Trash2, Settings, Shield, ArrowLeft } from 'lucide-react';
import Loader from '@/components/ui/loader';
import { useAuth } from '@/contexts/AuthenticationContext';
import { GoogleAuthProvider, reauthenticateWithPopup } from 'firebase/auth';

export default function SettingsPage() {
  const { firebaseUser } = useAuth();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [reauthenticating, setReauthenticating] = useState(false);

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      if (!firebaseUser) return;

      try {
        const userData = await getUser(firebaseUser.uid);
        if (userData) {
          setUser(userData);
        }
      } catch (error) {
        console.error('Error loading user:', error);
        toast.error('Failed to load user settings');
      } finally {
        setLoading(false);
      }
    };

    loadUser();

  }, [firebaseUser]);

  // Helper function to delete cookies
  const deleteCookie = (name: string) => {
    document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
  };

  // Re-authenticate user before sensitive operations
  const reauthenticateUser = async (): Promise<boolean> => {
    if (!firebaseUser) return false;

    setReauthenticating(true);
    try {
      const provider = new GoogleAuthProvider();
      await reauthenticateWithPopup(firebaseUser, provider);
      toast.success('Authentication confirmed');
      return true;
    } catch (error) {
      console.error('Re-authentication failed:', error);
      toast.error('Authentication failed. Please try again.');
      return false;
    } finally {
      setReauthenticating(false);
    }
  };

  // Delete account
  const handleDeleteAccount = async () => {
    if (!user || !firebaseUser) return;

    // First, re-authenticate the user
    const isReauthenticated = await reauthenticateUser();
    if (!isReauthenticated) {
      return;
    }

    setDeleting(true);

    try {
      // Clear the auth cookie first to prevent auth state listener from interfering
      deleteCookie('firebaseIdToken');

      // Delete the account (this will trigger auth state change but cookie is already cleared)
      await deleteUserAccount(user.uid, firebaseUser);

      // Show success message
      toast.success('Account deleted successfully. You can sign in again anytime.');

      // Wait a bit for user to see the message, then redirect
      setTimeout(() => {
        router.replace('/login');
      }, 2000);

    } catch (error) {
      console.error('Error deleting account:', error);
      
      // Handle specific Firebase errors
      if (error instanceof Error && error.message.includes('auth/requires-recent-login')) {
        toast.error('Please re-authenticate and try again.');
      } else {
        toast.error('Failed to delete account. Please try again.');
      }
      
      setDeleting(false); // Always re-enable UI on error
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader text="Loading settings..." />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">User not found</h1>
          <Button onClick={() => router.push('/dashboard')}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Full screen overlay during deletion */}
      {deleting && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-8 shadow-xl text-center">
            <Loader text="" />
            <h3 className="text-lg font-semibold mt-4 mb-2">Deleting Account</h3>
            <p className="text-gray-600">Please wait while we delete your account and all data...</p>
          </div>
        </div>
      )}

      {/* Re-authentication overlay */}
      {reauthenticating && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-8 shadow-xl text-center">
            <Loader text="" />
            <h3 className="text-lg font-semibold mt-4 mb-2">Confirming Identity</h3>
            <p className="text-gray-600">Please complete the authentication process...</p>
          </div>
        </div>
      )}

      <div className="max-w-2xl mx-auto py-8 px-4">
        {/* Header with Back Button */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </div>
          <div className="flex items-center gap-3 mb-2">
            <Settings className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Account Settings</h1>
          </div>
          <p className="text-gray-600">Manage your subscription and account</p>
        </div>

        <div className="grid gap-6">
          {/* Subscription */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5" />
                Subscription Plan
              </CardTitle>
              <CardDescription>
                Your current plan and features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Current Plan:</span>
                      <Badge variant="secondary">
                        FREE
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500">
                      Access to 2 free themes and basic features
                    </p>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">Premium Plan:</span>
                        <Badge variant="outline" className="text-orange-600 border-orange-200">
                          COMING SOON
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500">
                        Premium themes, custom domains, and advanced features
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <Shield className="w-5 h-5" />
                Danger Zone
              </CardTitle>
              <CardDescription>
                Irreversible actions that will permanently affect your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" className="flex items-center gap-2">
                    <Trash2 className="w-4 h-4" />
                    Delete Account
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Account & All Data?</AlertDialogTitle>
                    <AlertDialogDescription asChild>
                      <div className="space-y-3">
                        <div>
                          <strong>This action cannot be undone.</strong> We will permanently delete:
                        </div>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          <li>Your user account and profile</li>
                          <li>All your portfolio data and content</li>
                          <li>All associated files and images</li>
                          <li>Your account settings and preferences</li>
                        </ul>
                        <div className="text-sm text-gray-600">
                          <strong>Note:</strong> You can sign in again anytime to create a new account, but all your current data will be lost forever.
                        </div>
                        <div className="text-sm text-orange-600 bg-orange-50 p-3 rounded-md">
                          <strong>Security Notice:</strong> You will need to re-authenticate with Google to confirm your identity before account deletion.
                        </div>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteAccount}
                      disabled={deleting || reauthenticating}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {deleting ? (
                        <div className="flex items-center gap-2">
                          <Loader text="" />
                          <span>Deleting account and all data...</span>
                        </div>
                      ) : reauthenticating ? (
                        <div className="flex items-center gap-2">
                          <Loader text="" />
                          <span>Authenticating...</span>
                        </div>
                      ) : (
                        'Yes, Delete Everything'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
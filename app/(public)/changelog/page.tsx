"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Calendar, Sparkles, Shield, Users, Zap, Star, Gift } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function ChangelogPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Link href="/">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Button>
            </Link>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">What&apos;s New</h1>
                <p className="text-gray-600">Latest features and improvements</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Latest Release */}
        <div className="mb-8">
          <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="default" className="bg-blue-600 hover:bg-blue-700">
                    <Sparkles className="w-3 h-3 mr-1" />
                    Latest
                  </Badge>
                  <CardTitle className="text-xl">Version 1.1 - Quick Improvements</CardTitle>
                </div>
                <span className="text-sm text-gray-500">July 15, 2025</span>
              </div>
              <CardDescription className="text-base">
                Quick fixes and improvements based on initial user feedback
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* User Management */}
              <div className="flex gap-4">
                <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Improved User Experience</h3>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Added account settings page for better profile management</li>
                    <li>• Enhanced user data handling and privacy protection</li>
                    <li>• Added account deletion option for user control</li>
                  </ul>
                </div>
              </div>

              {/* Security */}
              <div className="flex gap-4">
                <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Security & Stability</h3>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Enhanced database security with better user data protection</li>
                    <li>• Improved authentication system reliability</li>
                    <li>• Fixed various bugs and improved error handling</li>
                    <li>• Better data validation and security measures</li>
                  </ul>
                </div>
              </div>

              {/* Performance */}
              <div className="flex gap-4">
                <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                  <Zap className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Performance & Bug Fixes</h3>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Fixed portfolio slug generation for cleaner URLs</li>
                    <li>• Improved loading times and responsiveness</li>
                    <li>• Better error messages and user feedback</li>
                    <li>• Code cleanup and optimization improvements</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Previous Releases */}
        <div className="space-y-6">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Previous Updates
          </h2>

          {/* Version 1.0 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Version 1.0 - Platform Launch</CardTitle>
                <span className="text-sm text-gray-500">July 14, 2025</span>
              </div>
              <CardDescription>
                Initial release with complete portfolio building platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <Gift className="w-4 h-4 text-green-600" />
                    Export Your Portfolio
                  </h4>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Download your portfolio as a complete website</li>
                    <li>• Host anywhere - no dependencies required</li>
                    <li>• Perfect pixel-by-pixel copy of your live site</li>
                    <li>• Mobile-responsive exported sites</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-purple-600" />
                    Multiple Themes
                  </h4>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Modern theme for developers and tech professionals</li>
                    <li>• Creative Minimalist theme for designers</li>
                    <li>• Consistent editing experience across themes</li>
                  </ul>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4 mt-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Core Features</h4>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Live portfolio editor with real-time preview</li>
                    <li>• Google authentication for easy sign-up</li>
                    <li>• Image upload and optimization</li>
                    <li>• Public portfolio publishing with custom URLs</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Portfolio Sections</h4>
                  <ul className="text-gray-600 space-y-1 text-sm">
                    <li>• Professional experience showcase</li>
                    <li>• Skills and expertise display</li>
                    <li>• Project portfolio with images and links</li>
                    <li>• Contact information and social links</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coming Soon */}
        <div className="mt-12">
          <Card className="border-dashed border-2 border-gray-300 bg-gray-50">
            <CardHeader className="text-center">
              <CardTitle className="text-lg text-gray-700 flex items-center justify-center gap-2">
                <Sparkles className="w-5 h-5" />
                Coming Soon
              </CardTitle>
              <CardDescription>
                Exciting features we&apos;re working on for future releases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="p-3 bg-blue-100 rounded-lg inline-block mb-3">
                    <Star className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Premium Themes</h4>
                  <p className="text-gray-600 text-sm">
                    Advanced theme designs with premium customization options
                  </p>
                </div>
                <div className="text-center">
                  <div className="p-3 bg-green-100 rounded-lg inline-block mb-3">
                    <Zap className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Custom Domains</h4>
                  <p className="text-gray-600 text-sm">
                    Connect your own domain to your portfolio
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            Have suggestions for new features? We&apos;d love to hear from you!
          </p>
          <Link href="/dashboard">
            <Button className="bg-blue-600 hover:bg-blue-700">
              Start Building Your Portfolio
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ExternalLink, Trash2 } from "lucide-react";
import { getThemeById } from "@/themes/theme-registry";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
}

interface CurrentPortfolioCardProps {
  portfolio: Portfolio;
  onExport?: () => void;
  isExporting?: boolean;
  onDelete?: () => void;
}

export default function CurrentPortfolioCard({
  portfolio,
  onExport,
  isExporting = false,
  onDelete
}: CurrentPortfolioCardProps) {
  const currentTheme = getThemeById(portfolio.templateId);

  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-200">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Portfolio Info & Actions */}
        <div className="space-y-4">
          {/* Status Badge */}
          <Badge
            variant={portfolio.isPublished ? "default" : "secondary"}
            className={`w-fit ${portfolio.isPublished
              ? "bg-green-100 text-green-800 border-green-200"
              : "bg-yellow-100 text-yellow-800 border-yellow-200"
            }`}
          >
            {portfolio.isPublished ? "Published" : "Draft"}
          </Badge>

          {/* Title */}
          <h3 className="text-xl font-semibold text-gray-900">Current Portfolio</h3>

          {/* Theme Description */}
          <p className="text-sm text-gray-600">
            Theme: {currentTheme?.name || "Unknown"} - {currentTheme?.description || "A clean and modern theme for showcasing your work."}
          </p>

          {/* Edit Portfolio Button */}
          <div className="pt-2">
            <Button asChild variant="outline" className="bg-gray-50 hover:bg-gray-100">
              <Link href="/portfolio">Edit Portfolio</Link>
            </Button>
          </div>

          {/* Action Buttons Row */}
          <div className="flex items-center gap-3 pt-4">
            <Button
              onClick={onDelete}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:text-red-700 hover:bg-red-50 hover:border-red-300"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>

            <Button
              onClick={onExport}
              disabled={isExporting || !portfolio.isPublished}
              className="bg-brandAccent hover:bg-brandAccent/90 text-white shadow-sm hover:shadow-md"
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                "Export Site"
              )}
            </Button>
          </div>

          {/* View Live Button (if published) */}
          {portfolio.isPublished && (
            <div className="pt-2">
              <Button asChild variant="outline" className="text-blue-600 border-blue-200 hover:bg-blue-50">
                <Link href={`/${portfolio.slug}`} target="_blank">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Live
                </Link>
              </Button>
            </div>
          )}
        </div>

        {/* Right Column - Portfolio Preview */}
        <div className="relative group">
          <div className="w-full h-48 lg:h-56 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden relative">
            {currentTheme?.preview ? (
              <Image
                src={currentTheme.preview}
                alt={`${currentTheme.name} preview`}
                fill
                className="object-cover object-top"
              />
            ) : (
              <div className="bg-white rounded-lg p-4 shadow-sm h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-2"></div>
                  <div className="text-xs font-medium text-gray-700">Portfolio</div>
                  <div className="w-12 h-1 bg-gray-300 rounded mx-auto mt-1"></div>
                </div>
              </div>
            )}

            {/* Hover Overlay with Edit Button */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
              <Button
                asChild
                className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-90 group-hover:scale-100 bg-white text-gray-900 hover:bg-gray-100"
              >
                <Link href="/portfolio">
                  Edit Portfolio
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

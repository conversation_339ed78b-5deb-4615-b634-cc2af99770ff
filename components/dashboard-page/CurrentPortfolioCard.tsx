"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, ExternalLink, Trash2 } from "lucide-react";
import { getThemeById } from "@/themes/theme-registry";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
}

interface CurrentPortfolioCardProps {
  portfolio: Portfolio;
  onExport?: () => void;
  isExporting?: boolean;
  onDelete?: () => void;
}

export default function CurrentPortfolioCard({
  portfolio,
  onExport,
  isExporting = false,
  onDelete
}: CurrentPortfolioCardProps) {
  const currentTheme = getThemeById(portfolio.templateId);

  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 border border-gray-200">
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        {/* Left side - Portfolio info */}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Badge
              variant={portfolio.isPublished ? "default" : "secondary"}
              className={`${portfolio.isPublished
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-yellow-100 text-yellow-800 border-yellow-200"
              }`}
            >
              {portfolio.isPublished ? "Published" : "Draft"}
            </Badge>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-2">Current Portfolio</h3>

          <p className="text-sm text-gray-600 mb-6">
            Theme: {currentTheme?.name || "Unknown"} - {currentTheme?.description || "A clean and modern theme for showcasing your work."}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 mb-6">
            <Button asChild variant="outline" className="bg-gray-50 hover:bg-gray-100">
              <Link href="/portfolio">Edit Portfolio</Link>
            </Button>

            {portfolio.isPublished && (
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <Link href={`/${portfolio.slug}`} target="_blank">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Live
                </Link>
              </Button>
            )}
          </div>

          {/* Bottom action buttons */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <Button
              onClick={onDelete}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:text-red-700 hover:bg-red-50 hover:border-red-300 w-full sm:w-auto"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>

            <Button
              onClick={onExport}
              disabled={isExporting || !portfolio.isPublished}
              className="bg-brandAccent hover:bg-brandAccent/90 text-white shadow-sm hover:shadow-md w-full sm:w-auto"
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                "Export Site"
              )}
            </Button>
          </div>
        </div>

        {/* Right side - Portfolio preview */}
        <div className="flex justify-center lg:justify-end">
          <div className="w-full max-w-sm lg:w-64 h-40 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center relative overflow-hidden">
            {currentTheme?.preview ? (
              <Image
                src={currentTheme.preview}
                alt={`${currentTheme.name} preview`}
                fill
                className="object-cover object-top"
              />
            ) : (
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-2"></div>
                  <div className="text-xs font-medium text-gray-700">Portfolio</div>
                  <div className="w-12 h-1 bg-gray-300 rounded mx-auto mt-1"></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

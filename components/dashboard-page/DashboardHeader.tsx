"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Loader2, LogOut, Settings } from "lucide-react";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface DashboardHeaderProps {
  user: User | null;
  onSignOut: () => Promise<void>;
  isSigningOut: boolean;
}

export default function DashboardHeader({ user, onSignOut, isSigningOut }: DashboardHeaderProps) {
  const router = useRouter();

  return (
    <nav className="bg-background border-b">
      <div className="flex items-center justify-between mx-auto px-8 py-4">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <Link
            href="/"
            className="flex items-center gap-2 relative z-10"
          >
            <Image src="/icon.svg" alt="logo" width={32} height={32} />
            <span className="font-bold text-xl lg:text-2xl gradient-text">
              Profolify
            </span>
          </Link>
        </div>

        {/* User Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-12 w-12 rounded-full p-0 hover:bg-transparent">
              <Avatar className="h-12 w-12 border-2 hover:border-brandPrimary transition-colors">
                <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
                <AvatarFallback>{user?.displayName?.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <div className="flex items-center justify-start gap-2 p-2">
              <div className="flex flex-col space-y-1 leading-none">
                <p className="font-medium">{user?.displayName}</p>
                <p className="w-[200px] truncate text-sm text-muted-foreground">
                  {user?.email}
                </p>
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/settings')}>
              <Settings className="mr-2 h-4 w-4 hover:text-white" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onSignOut} disabled={isSigningOut}>
              {isSigningOut ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogOut className="mr-2 h-4 w-4 hover:text-white" />}
              <span>{isSigningOut ? 'Signing out...' : 'Sign out'}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </nav>
  );
}

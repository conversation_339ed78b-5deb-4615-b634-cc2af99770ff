"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Loader2, Check } from "lucide-react";
import { toast } from "sonner";
import { getAllThemes, type ThemeConfig } from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplate } from "@/lib/portfolio-api";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
}

interface ThemeGridProps {
  user: User | null;
  portfolio: Portfolio | null;
}

export default function ThemeGrid({ user, portfolio }: ThemeGridProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      await queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success("Theme applied successfully!");
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
    },
    onSettled: () => {
      setLoadingThemeId(null);
    },
  });

  // For new portfolio - template selection
  const createPortfolioMutation = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: (newPortfolio) => {
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);
      toast.success("Portfolio created! Redirecting to editor...");
      router.push('/portfolio');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create portfolio: ${error.message}`);
    },
    onSettled: () => {
      setLoadingThemeId(null);
    }
  });

  const handleThemeAction = (themeId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme
      createPortfolioMutation.mutate({ user, templateId: themeId });
    }
  };

  const themes = getAllThemes();

  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-200">
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Try Our Themes</h3>
        <p className="text-sm text-gray-600">
          You can use any of our themes and also can switch back between themes after creation. Your content will be preserved when switching.
        </p>
      </div>

      {/* Horizontal theme grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {themes.map((theme, index) => {
          const isCurrentTheme = portfolio?.templateId === theme.id;
          const isLoadingThisTheme = loadingThemeId === theme.id;

          return (
            <div key={theme.id} className="group">
              {/* Theme preview */}
              <div className="relative aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden mb-3">
                <Image
                  src={theme.preview || '/thumbnails/default-theme.jpg'}
                  alt={`${theme.name} preview`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />

                {/* Current theme indicator */}
                {isCurrentTheme && (
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <div className="bg-white/95 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-800">Current</span>
                    </div>
                  </div>
                )}

                {/* Loading overlay */}
                {isLoadingThisTheme && (
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center">
                    <Loader2 className="w-6 h-6 animate-spin text-brandPrimary" />
                  </div>
                )}
              </div>

              {/* Theme info */}
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-1">{theme.name}</h4>

                {/* Action button */}
                <Button
                  onClick={() => handleThemeAction(theme.id)}
                  disabled={isCurrentTheme || isLoadingThisTheme}
                  variant={isCurrentTheme ? "secondary" : "outline"}
                  size="sm"
                  className="w-full"
                >
                  {isLoadingThisTheme ? (
                    <>
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      {portfolio ? "Applying..." : "Creating..."}
                    </>
                  ) : isCurrentTheme ? (
                    "Current Theme"
                  ) : (
                    portfolio ? "Apply" : "Use Theme"
                  )}
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

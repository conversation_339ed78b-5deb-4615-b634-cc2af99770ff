"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Check, Palette, Sparkles, Download } from "lucide-react";
import { toast } from "sonner";
import { getAllThemes, type ThemeConfig } from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplate } from "@/lib/portfolio-api";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
}

interface ThemeSelectorProps {
  user: User | null;
  portfolio: Portfolio | null;
  onTemplateSelect?: (templateId: string) => void;
}

export default function ThemeSelector({ user, portfolio, onTemplateSelect }: ThemeSelectorProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      await queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success("Theme applied successfully!");
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
    },
    onSettled: () => {
      setLoadingThemeId(null);
    },
  });

  // For new portfolio - template selection
  const createPortfolioMutation = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: (newPortfolio) => {
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);
      toast.success("Portfolio created! Redirecting to editor...");
      router.push('/portfolio');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create portfolio: ${error.message}`);
    },
    onSettled: () => {
      setLoadingThemeId(null);
    }
  });

  const handleThemeAction = (themeId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme
      createPortfolioMutation.mutate({ user, templateId: themeId });
    }
  };

  const themes = getAllThemes();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5 text-brandPrimary" />
          {portfolio ? "Switch Theme" : "Choose Your Theme"}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {portfolio 
            ? "Your content will be preserved when switching themes." 
            : "Select a template to get started with your portfolio."
          }
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-2 gap-6">
          {themes.map((theme) => {
            const isCurrentTheme = portfolio?.templateId === theme.id;
            const isLoadingThisTheme = loadingThemeId === theme.id;

            return (
              <div
                key={theme.id}
                className={`group relative bg-white rounded-xl border hover:shadow-lg transition-all duration-300 overflow-hidden ${
                  isCurrentTheme 
                    ? 'border-brandPrimary ring-2 ring-brandPrimary/20' 
                    : 'border-gray-200 hover:border-brandPrimary/30'
                }`}
              >
                {/* Theme Preview */}
                <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                  <Image
                    src={theme.preview || '/thumbnails/default-theme.jpg'}
                    alt={`${theme.name} template preview`}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  
                  {/* Current Theme Overlay */}
                  {isCurrentTheme && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <div className="bg-white/95 backdrop-blur-sm rounded-full px-4 py-2 flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-600" />
                        <span className="font-medium text-gray-800">Current Theme</span>
                      </div>
                    </div>
                  )}

                  {/* Theme Category Badge */}
                  <div className="absolute top-3 left-3">
                    <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm text-gray-700">
                      {theme.category}
                    </Badge>
                  </div>

                  {/* Free Badge */}
                  <div className="absolute top-3 right-3">
                    <Badge className="bg-green-100/90 text-green-700 border-green-200">
                      Free
                    </Badge>
                  </div>
                </div>

                {/* Theme Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-brandPrimary transition-colors">
                    {theme.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {theme.description}
                  </p>

                  {/* Features */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    <Badge variant="outline" className="text-xs">
                      <Check className="w-3 h-3 mr-1" />
                      Responsive
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Download className="w-3 h-3 mr-1" />
                      Export Ready
                    </Badge>
                  </div>

                  {/* Action Button */}
                  <Button
                    onClick={() => handleThemeAction(theme.id)}
                    disabled={isCurrentTheme || isLoadingThisTheme}
                    className="w-full"
                    variant={isCurrentTheme ? "secondary" : "default"}
                  >
                    {isLoadingThisTheme ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {portfolio ? "Applying..." : "Creating..."}
                      </>
                    ) : isCurrentTheme ? (
                      <>
                        <Check className="mr-2 h-4 w-4" />
                        Current Theme
                      </>
                    ) : (
                      <>
                        {portfolio ? (
                          <>
                            <Palette className="mr-2 h-4 w-4" />
                            Apply Theme
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-4 w-4" />
                            Use This Template
                          </>
                        )}
                      </>
                    )}
                  </Button>
                </div>

                {/* Loading Overlay */}
                {isLoadingThisTheme && (
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10">
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 animate-spin text-brandPrimary mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-700">
                        {portfolio ? "Applying theme..." : "Creating portfolio..."}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface WelcomeHeaderProps {
  user: User | null;
  hasPortfolio: boolean;
}

export default function WelcomeHeader({ user, hasPortfolio }: WelcomeHeaderProps) {
  return (
    <div className="flex items-center gap-4 mb-8">
      <Avatar className="h-16 w-16 border-2 border-gray-200">
        <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
        <AvatarFallback className="text-lg font-semibold bg-gradient-to-br from-brandPrimary to-brandSecondary text-white">
          {user?.displayName?.charAt(0).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          {hasPortfolio
            ? `Welcome back, ${user?.displayName}!`
            : `Welcome to Profolify, ${user?.displayName?.split(' ')[0]}!`
          }
        </h1>
        <p className="text-gray-600">
          {hasPortfolio
            ? "Manage your portfolio and showcase your work to the world."
            : "Let's create your professional portfolio in minutes."
          }
        </p>
      </div>
    </div>
  );
}

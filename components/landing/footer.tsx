import React from "react";
import Link from "next/link";
import { Github, Twitter, Mail, ExternalLink, User } from "lucide-react";
import Image from "next/image";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-backgroundSecondary border-t border-borderPrimary/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Brand section */}
          <div className="flex items-center gap-3">
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
              <Image src="/icon.svg" alt="logo" width={24} height={24} />
              <span className="font-bold text-xl  gradient-text">
                Profolify
              </span>
            </Link>
          </div>

          {/* Links */}
          <div className="flex items-center gap-6 text-sm">
            <Link href="#features" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Features
            </Link>
            <Link href="#themes" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Themes
            </Link>
            <Link href="#pricing" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              Pricing
            </Link>
            <Link href="/changelog" className="text-textSecondary hover:text-brandPrimary transition-colors duration-200">
              What&apos;s New
            </Link>
          </div>

          {/* Social links */}
          <div className="flex items-center gap-3">
            <Link
              href="https://github.com/darshanbajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <Github className="w-4 h-4" />
            </Link>
            <Link
              href="https://x.com/darshan_bajgain"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter"
            >
              <Twitter className="w-4 h-4" />
            </Link>
            <Link
              href="mailto:<EMAIL>"
              className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
              aria-label="Email"
            >
              <Mail className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-8 pt-6 border-t border-borderPrimary/30">
          {/* Copyright */}
          <div className="text-center mb-4">
            <p className="text-sm text-textSecondary">© {currentYear} Profolify. All rights reserved.</p>
          </div>

          {/* Meet the Creator */}
          <div className="text-center">
            <div className="inline-flex items-center gap-2 text-sm text-textSecondary mb-3">
              <User className="w-4 h-4 text-brandPrimary" />
              <span>Meet the Creator / Developer</span>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
              <span className="text-sm font-medium text-textPrimary">
                Darshan Bajgain
              </span>

              <div className="flex items-center gap-2">
                <a
                  href="https://darshanbajgain.com.np/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-brandPrimary hover:text-brandSecondary border border-brandPrimary/30 hover:border-brandPrimary/50 rounded-lg hover:bg-brandPrimary/5 transition-all duration-200"
                >
                  <ExternalLink className="w-3 h-3" />
                  Portfolio
                </a>

                <a
                  href="https://github.com/darshanbajgain"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-textSecondary hover:text-textPrimary border border-borderPrimary/50 hover:border-borderPrimary rounded-lg hover:bg-backgroundSecondary transition-all duration-200"
                >
                  <Github className="w-3 h-3" />
                  GitHub
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}


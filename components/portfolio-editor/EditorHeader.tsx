"use client";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { LogOut, LayoutDashboard, Loader2, Rocket } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthenticationContext";
import { useLogout } from "@/hooks/use-logout";
import { useState } from "react";
import Image from "next/image";

interface EditorHeaderProps {
  isPublishing: boolean;
  isDirty: boolean; // Keep for publish button logic
  onTogglePublish: () => void;
}

export default function EditorHeader(props: EditorHeaderProps) {
  const { user } = useAuth();
  const logout = useLogout();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };

  const EditorBranding = () => (
    <div className="flex items-center gap-5">
      <div className="flex items-center gap-4">
        {/* <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center">
          <span className="text-white font-bold text-sm">P</span>
        </div> */}
        <Link
          href="/"
          className="flex items-center gap-2 relative z-10"
        >
          {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
          <Image src="/icon.svg" alt="logo" width={32} height={32} />
          <span className="font-bold text-xl lg:text-2xl gradient-text">
            Profolify
          </span>
        </Link>
        <div className="flex flex-col items-start">
          <h1 className="text-sm font-bold text-slate-800">Portfolio Editor</h1>
          <p className="text-xs text-slate-500">Design your professional presence</p>
        </div>
      </div>
    </div>
  );

  const ActionButtons = () => (
    <div className="flex items-center gap-3">
      {/* <Button asChild variant="outline" className="bg-white hover:bg-slate-50 border-slate-200 text-sm">
        <Link href="/dashboard">
          <Home className="mr-2 h-4 w-4" />
          Dashboard
        </Link>
      </Button> */}
      {/* <Button asChild variant="outline" className="bg-white hover:bg-slate-50 border-slate-200 text-sm">
        <Link href="/dashboard/themes">
          <Palette className="mr-2 h-4 w-4" />
          Change Theme
        </Link>
      </Button> */}
      <Button
        onClick={props.onTogglePublish}
        disabled={props.isPublishing}
        className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg"
      >
        {props.isPublishing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Rocket className="mr-2 h-4 w-4" />}
        {props.isPublishing ? "Publishing..." : "Publish"}
      </Button>
    </div>
  );

  const UserMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="p-0 h-10 w-10 rounded-full hover:bg-slate-100">
          <Avatar className="h-10 w-10 border-2 border-slate-200 hover:border-blue-400 transition-colors">
            <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
            <AvatarFallback className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold">
              {user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56 z-[9999] bg-white border-slate-200 shadow-xl">
        <div className="flex flex-col px-3 py-3 bg-slate-50 border-b border-slate-200">
          <span className="font-semibold text-slate-800 truncate">{user?.displayName || 'User'}</span>
          <span className="text-xs text-slate-500 truncate">{user?.email || 'No email'}</span>
        </div>
        <DropdownMenuItem asChild className="hover:bg-blue-50">
          <Link href="/dashboard">
            <LayoutDashboard className="mr-2 h-4 w-4 hover:text-white" />
            Dashboard
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator className="bg-slate-200" />
        <DropdownMenuItem onClick={handleSignOut} disabled={isSigningOut} className="hover:bg-red-50 text-red-600">
          {isSigningOut ? <Loader2 className="mr-2 h-4 w-4  animate-spin" /> : <LogOut className="mr-2 h-4 w-4 hover:text-white" />}
          {isSigningOut ? 'Signing out...' : 'Sign out'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <header className="sticky top-0 z-50  w-full bg-white/95 backdrop-blur-sm border-b border-slate-200 shadow-sm">
      <div className="flex justify-between mx-auto px-8 py-4  gap-4">
        <EditorBranding />
        <div className="flex items-center gap-3">
          <ActionButtons />
          <UserMenu />
        </div>
      </div>
    </header>
  );
}

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

// const buttonVariants = cva(
//   "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-backgroundPrimary",
//   {
//     variants: {
//       variant: {
//         // --- THIS IS THE PART WE ARE CHANGING ---

//         // The main call-to-action button
//         default:
//           "",

//         // The button for dangerous actions
//         destructive:
//           "",

//         // A button with a border and transparent background
//         outline:
//           "text-lg px-8 py-4 glass-effect hover:shadow-lg transition-all duration-300 rounded-lg font-semibold text-textPrimary",

//         // A subtler button with a light background
//         secondary:
//           "",

//         // A button with no background or border, just text
//         ghost:
//           "hover:bg-accent hover:text-textPrimary",

//         // A button that looks like a hyperlink
//         link: "text-accent underline-offset-4 hover:underline",

//         plain: "hover:text-accent bg-transparent",

//         cta: "text-lg px-8 py-4 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-2xl hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-300 group rounded-lg font-semibold text-white flex items-center justify-center gap-2",

//         signIn: "px-6 py-2 bg-gradient-to-r from-brandPrimary to-brandSecondary hover:shadow-lg hover:shadow-brandPrimary/25 transform hover:scale-105 transition-all duration-200 rounded-lg font-semibold text-white"
//       },
//       size: {
//         default: "h-12 px-4 py-2",
//         sm: "h-9 rounded-md px-3",
//         lg: "h-16 rounded-md px-8",
//         icon: "h-10 w-10",
//       },
//     },
//     defaultVariants: {
//       variant: "default",
//       size: "default",
//     },
//   }
// )

// The rest of the component remains the same

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-backgroundPrimary",
  {
    variants: {
      variant: {
        // Primary gradient button with enhanced styling
        default:
          "bg-gradient-to-r from-brandPrimary to-brandSecondary text-white hover:shadow-xl hover:shadow-brandPrimary/30 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold",

        // Destructive action button
        destructive:
          "bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-lg hover:shadow-red-500/25 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold",

        // Secondary button with glass effect
        secondary:
          "bg-brandSecondary/80 backdrop-blur-sm text-white hover:bg-brandSecondary border border-borderPrimary/50 hover:border-borderPrimary rounded-xl font-medium hover:shadow-md",

        // Outline button with glass effect
        outline:
          "border-2 border-brandPrimary/30 bg-transparent backdrop-blur-sm text-brandPrimary hover:bg-brandPrimary/10 hover:border-brandPrimary/50 hover:shadow-lg hover:shadow-brandPrimary/20 rounded-xl font-semibold",

        // Ghost button
        ghost:
          "bg-transparent text-textPrimary hover:bg-backgroundSecondary/50 hover:text-brandPrimary rounded-xl font-medium",

        // Link style button
        link:
          "text-brandPrimary underline-offset-4 hover:underline font-medium",

        // Plain text button
        plain:
          "bg-transparent text-textSecondary hover:text-brandPrimary font-medium",

        // Theme button with accent color
        theme:
          "bg-gradient-to-r from-brandAccent to-brandSecondary text-white hover:shadow-lg hover:shadow-brandAccent/25 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold",

        // Large CTA button
        cta:
          "bg-gradient-to-r from-brandPrimary via-brandSecondary to-brandAccent text-white hover:shadow-2xl hover:shadow-brandPrimary/30 transform hover:scale-105 active:scale-[0.98] rounded-2xl font-bold text-lg",

        // Sign in button
        signIn:
          "bg-gradient-to-r from-brandPrimary to-brandSecondary text-white hover:shadow-lg hover:shadow-brandPrimary/25 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold",

        // Glass effect button
        glass:
          "bg-white/10 backdrop-blur-md border border-white/20 text-textPrimary hover:bg-white/20 hover:shadow-lg rounded-xl font-medium",

        // Success button
        success:
          "bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:shadow-lg hover:shadow-green-500/25 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold",

        // Warning button
        warning:
          "bg-gradient-to-r from-amber-500 to-orange-600 text-white hover:shadow-lg hover:shadow-amber-500/25 transform hover:scale-[1.02] active:scale-[0.98] rounded-xl font-semibold"
      },
      size: {
        xs: "h-8 px-3 text-xs",
        sm: "h-9 px-4 text-sm",
        default: "h-11 px-6 text-sm",
        lg: "h-12 px-8 text-base",
        xl: "h-14 px-10 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
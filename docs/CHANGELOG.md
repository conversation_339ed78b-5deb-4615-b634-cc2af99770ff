# Profolify Changelog

All notable changes to this project are documented here.

---

## [1.2.0] – 2025-07-17

### ✨ New Features
- **Improved Publish Flow:** Publishing a portfolio now automatically opens the live site in a new tab and redirects the user to the dashboard for a smoother workflow.

### 🛠️ Improvements
- **Export Reliability:** Fixed a critical race condition in the live DOM capture system by increasing the render delay. This prevents incomplete or empty `index.html` files from being generated.
- **Theme CSS Scalability:** Refactored global CSS to use a generic `.theme-navbar` class for editor overrides, removing the need to add theme-specific rules and improving maintainability.

### 🐞 Bug Fixes
- **Theme Sync Errors:** Corrected the file path for the Modern theme's CSS in the sync configuration, resolving errors during the theme synchronization process.

---

## [1.1.0] – 2025-07-15

### ✨ New Features
- **Account Settings:** Comprehensive settings page for user profile and account management.
- **Account Deletion:** Users can now securely delete their account and all data.
- **Improved Google Auth:** Smoother sign-in flow and better loading states.

### 🛠️ Improvements
- **Security:** Enhanced user data protection and Firestore rules.
- **Performance:** Faster loading, cleaner URLs, and improved error handling.
- **Codebase:** Removed unused code, improved stability, and optimized state management.

### 🐞 Bug Fixes
- Fixed: Various issues reported by early users.
- Fixed: Edge cases in export and theme switching.
- Fixed: UI glitches on mobile devices.

---

## [1.0.0] – 2025-07-14

### 🚀 Initial Launch
- **Live Editing:** Real-time, WYSIWYG portfolio editor.
- **Theme System:** Modular, extensible themes (Modern, Creative Minimalist).
- **Export:** Pixel-perfect static export with Live DOM Capture.
- **Google Authentication:** Secure, one-click sign-in.
- **Cloudinary Integration:** Fast, optimized image delivery.
- **Mobile-First:** Fully responsive design.
- **Default Content:** Sample data for instant onboarding.

---

## [Unreleased]

- **Custom Domains:** (Planned) Map portfolios to user-owned domains.
- **Premium Themes:** (Planned) Paid themes and Stripe integration.
- **Theme Customization:** (Planned) User-driven color and font customization.
- **Email/Password Auth:** (Planned) Broader sign-in options.
- **Analytics:** (Planned) Usage and engagement tracking.

---

## How to Contribute

- Submit a pull request with your change.
- Add a new entry to this changelog under [Unreleased].

---

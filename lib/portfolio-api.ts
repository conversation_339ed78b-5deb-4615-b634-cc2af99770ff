import { doc, getDoc, updateDoc, deleteDoc, collection, query, where, getDocs, limit, addDoc, writeBatch } from "firebase/firestore";
import { firestore } from "./firebase";
import { PortfolioData, defaultPortfolioData, User } from "./types";
import { slugify } from "./utils";

// Get user's portfolios (multi-tenancy support)
export const getUserPortfolios = async (userId: string): Promise<PortfolioData[]> => {
    try {
        const q = query(collection(firestore, "portfolios"), where("userId", "==", userId));
        const querySnapshot = await getDocs(q);

        return querySnapshot.docs.map(doc => ({
            ...doc.data(),
            id: doc.id,
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as PortfolioData[];
    } catch (error) {
        console.error('Error getting user portfolios:', error);
        throw error;
    }
};

// Get single portfolio by ID
export const getPortfolio = async (portfolioId: string): Promise<PortfolioData | null> => {
    try {
        const docRef = doc(firestore, "portfolios", portfolioId);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            const data = docSnap.data();
            return {
                ...data,
                id: docSnap.id,
                createdAt: data.createdAt?.toDate() || new Date(),
                updatedAt: data.updatedAt?.toDate() || new Date(),
            } as PortfolioData;
        }

        return null;
    } catch (error) {
        console.error('Error getting portfolio:', error);
        throw error;
    }
};


// --- Function to generate a unique slug ---
export const generateUniqueSlug = async (displayName: string, userId: string): Promise<string> => {
    const baseSlug = slugify(displayName);
    let finalSlug = baseSlug;
    let counter = 2;

    // This loop will continue until it finds a slug that is not in use
    while (true) {
        const q = query(
            collection(firestore, "portfolios"),
            where("slug", "==", finalSlug)
        );
        const querySnapshot = await getDocs(q);

        // If the slug doesn't exist, we can use it!
        if (querySnapshot.empty) {
            break;
        }

        // If the slug exists, we need to check if it belongs to the current user.
        // If it does, they can keep their slug.
        const slugIsOwn = querySnapshot.docs.some(doc => doc.id === userId);
        if (slugIsOwn) {
            break;
        }

        // If the slug exists and belongs to someone else, append the counter.
        finalSlug = `${baseSlug}-${counter}`;
        counter++;
    }

    return finalSlug;
};


// --- NEW: Function to create a portfolio from a selected template ---
// Create new portfolio for user
export const createPortfolioFromTemplate = async ({ user, templateId }: { user: User; templateId: string; }): Promise<PortfolioData> => {
    try {
        // Create portfolio data with a temporary ID (will be replaced with actual ID)
        const portfolioData = defaultPortfolioData("temp", user.uid, user);
        portfolioData.templateId = templateId;

        // Create new portfolio document with complete data in one operation
        const portfoliosRef = collection(firestore, "portfolios");
        const docRef = await addDoc(portfoliosRef, portfolioData);

        // Update the document with the correct ID (keep the clean slug)
        const finalPortfolioData = { ...portfolioData, id: docRef.id };
        await updateDoc(docRef, {
            id: docRef.id
        });

        return finalPortfolioData;
    } catch (error) {
        console.error('Error creating portfolio:', error);
        throw error;
    }
};

// Update portfolio theme
export const updatePortfolioTheme = async (portfolioId: string, templateId: string): Promise<void> => {
    try {
        const docRef = doc(firestore, "portfolios", portfolioId);
        await updateDoc(docRef, {
            templateId: templateId,
            updatedAt: new Date(),
        });
    } catch (error) {
        console.error('Error updating portfolio theme:', error);
        throw error;
    }
};

// Update portfolio
export const updatePortfolio = async (portfolioId: string, updates: Partial<PortfolioData>): Promise<void> => {
    try {
        const docRef = doc(firestore, "portfolios", portfolioId);
        await updateDoc(docRef, {
            ...updates,
            updatedAt: new Date(),
        });
    } catch (error) {
        console.error('Error updating portfolio:', error);
        throw error;
    }
};

// Delete portfolio
export const deletePortfolio = async (portfolioId: string): Promise<void> => {
    try {
        if (!portfolioId) {
            throw new Error('Portfolio ID is required');
        }

        const docRef = doc(firestore, "portfolios", portfolioId);
        await deleteDoc(docRef);
    } catch (error) {
        console.error('Error deleting portfolio:', error);
        if (error instanceof Error) {
            throw new Error(`Failed to delete portfolio: ${error.message}`);
        }
        throw error;
    }
};

// Migration function to restore clean slugs
export const fixTempSlugs = async (): Promise<void> => {
    try {
        console.log('🔧 Starting migration to restore clean slugs...');

        // Get all portfolios
        const portfoliosRef = collection(firestore, "portfolios");
        const snapshot = await getDocs(portfoliosRef);

        const batch = writeBatch(firestore);
        let fixedCount = 0;

        snapshot.forEach((docSnapshot) => {
            const portfolio = docSnapshot.data() as PortfolioData;

            console.log(`📋 Found portfolio ${docSnapshot.id} with slug: "${portfolio.slug}"`);

            // Check if slug has suffixes that need to be cleaned
            if (portfolio.slug && (portfolio.slug.includes('-temp') || portfolio.slug.match(/-[a-zA-Z0-9]{6}$/))) {
                console.log(`🔧 Cleaning portfolio ${portfolio.id} slug: ${portfolio.slug}`);

                // Extract the clean name part (everything before the last dash and suffix)
                const cleanSlug = portfolio.userName ?
                    portfolio.userName.toLowerCase().replace(/\s+/g, '-') :
                    portfolio.slug.split('-').slice(0, -1).join('-');

                // Update the portfolio
                const docRef = doc(firestore, "portfolios", docSnapshot.id);
                batch.update(docRef, { slug: cleanSlug });

                fixedCount++;
                console.log(`✅ Will update slug from "${portfolio.slug}" to "${cleanSlug}"`);
            }
        });

        if (fixedCount > 0) {
            await batch.commit();
            console.log(`🎉 Successfully cleaned ${fixedCount} portfolio slugs!`);
        } else {
            console.log('✅ No portfolios with complex slugs found.');
        }

    } catch (error) {
        console.error('❌ Error cleaning slugs:', error);
        throw error;
    }
};

// Get portfolio by slug (for public viewing)
export const getPortfolioBySlug = async (slug: string): Promise<PortfolioData | null> => {
    try {
        const q = query(
            collection(firestore, "portfolios"),
            where("slug", "==", slug),
            where("isPublished", "==", true),
            limit(1)
        );
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
            const doc = querySnapshot.docs[0];
            const data = doc.data();
            return {
                ...data,
                id: doc.id,
                createdAt: data.createdAt?.toDate() || new Date(),
                updatedAt: data.updatedAt?.toDate() || new Date(),
            } as PortfolioData;
        }

        return null;
    } catch (error) {
        console.error('Error getting portfolio by slug:', error);
        throw error;
    }
};







// Upload a file to Cloudinary (no change)
export const uploadFile = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET!);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/upload`, {
        method: 'POST',
        body: formData,
    });

    if (!response.ok) throw new Error('File upload failed');
    const data = await response.json();
    return data.resource_type === 'image' ? data.secure_url.replace('/upload/', '/upload/f_auto,q_auto/') : data.secure_url;
};
"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { EditableText } from "@/components/custom-ui/EditableText";

import { Experience, SectionProps } from "@/lib/types";
import { Briefcase, Building, MapPin, Plus, TimerIcon, Trash2 } from "lucide-react";

interface ExperienceItemProps {
    experience: Experience;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Experience, value: string) => void;
    onDelete: (id: string) => void;
}

const ExperienceItem = ({ experience, isEditing, onUpdate, onDelete }: ExperienceItemProps) => {
    return (
        <div className="theme-creative-experience-item">
            <div className="theme-creative-experience-timeline-marker">
                <div className="theme-creative-experience-timeline-dot"></div>
                <div className="theme-creative-experience-timeline-line"></div>
            </div>

            <div className="theme-creative-experience-card">
                {isEditing ? (
                    <div className="theme-creative-experience-editor-layout">
                        <div className="theme-creative-experience-editor-row">
                            <div className="theme-creative-experience-editor-field">
                                <Briefcase className="theme-creative-experience-field-icon" size={20} />
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="theme-creative-experience-field-input"
                                    initialValue={experience.role}
                                    placeholder="Job Title"
                                    onSave={(value) => onUpdate(experience.id, 'role', value)}
                                />
                            </div>
                            <div className="theme-creative-experience-editor-field">
                            <TimerIcon className="theme-creative-experience-field-icon" size={20} />

                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="theme-creative-experience-field-input"
                                    initialValue={experience.duration}
                                    placeholder="e.g., April 2020 - June 2023"
                                    onSave={(value) => onUpdate(experience.id, 'duration', value)}
                                />
                            </div>
                        </div>
                        <div className="theme-creative-experience-editor-row">
                            <div className="theme-creative-experience-editor-field">
                            <Building className="theme-creative-experience-field-icon" size={20} />
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="theme-creative-experience-field-input"
                                    initialValue={experience.company}
                                    placeholder="Company Name"
                                    onSave={(value) => onUpdate(experience.id, 'company', value)}
                                />
                            </div>
                            <div className="theme-creative-experience-editor-field">
                            <MapPin className="theme-creative-experience-field-icon" size={20} />

                                <EditableText
                                    isEditing={isEditing}
                                    tagName="div"
                                    className="theme-creative-experience-field-input"
                                    initialValue={experience.location || ''}
                                    placeholder="Location"
                                    onSave={(value) => onUpdate(experience.id, 'location', value)}
                                />
                            </div>
                        </div>
                        <div className="theme-creative-experience-editor-description">
                            <EditableText
                                isEditing={isEditing}
                                tagName="div"
                                className="theme-creative-experience-field-input"
                                initialValue={experience.description || ''}
                                placeholder="Describe your key responsibilities and achievements..."
                                onSave={(value) => onUpdate(experience.id, 'description', value)}
                            />
                        </div>
                        <div className="theme-creative-experience-actions">
                            <button
                                onClick={() => onDelete(experience.id)}
                                className="theme-creative-experience-delete-btn"
                                title="Delete Experience"
                            >
                                <Trash2 className="theme-creative-experience-delete-icon" />
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className="theme-creative-experience-published-layout">
                        <div className="theme-creative-experience-header">
                            <div className="theme-creative-experience-title-section">
                                <div className="theme-creative-experience-role-row">
                                    <svg className="theme-creative-experience-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M20 6h-2V4c0-1.11-.89-2-2-2H8c-1.11 0-2 .89-2 2v2H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM8 4h8v2H8V4zm12 15H4V8h16v11z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <h3 className="theme-creative-experience-role">{experience.role}</h3>
                                </div>
                                <div className="theme-creative-experience-company-row">
                                    <svg className="theme-creative-experience-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M20 6h-2V4c0-1.11-.89-2-2-2H8c-1.11 0-2 .89-2 2v2H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM8 4h8v2H8V4zm12 15H4V8h16v11z"
                                            fill="currentColor"
                                        />
                                    </svg>
                                    <div className="theme-creative-experience-company">{experience.company}</div>
                                </div>
                            </div>
                            <div className="theme-creative-experience-meta-section">
                                <div className="theme-creative-experience-duration-badge">
                                    <svg className="theme-creative-experience-meta-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                                        <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2" />
                                    </svg>
                                    {experience.duration}
                                </div>
                                {experience.location && (
                                    <div className="theme-creative-experience-location-badge">
                                        <svg className="theme-creative-experience-meta-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" strokeWidth="2" />
                                            <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" />
                                        </svg>
                                        {experience.location}
                                    </div>
                                )}
                            </div>
                        </div>

                        {experience.description && (
                            <div className="theme-creative-experience-description">{experience.description}</div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export function CreativeExperience({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddExperience = () => {
        if (dispatch) {
            dispatch({ type: 'ADD_EXPERIENCE' });
        }
    };

    const handleUpdateExperience = (id: string, field: keyof Experience, value: string) => {
        if (dispatch) {
            const index = data.experiences.findIndex(exp => exp.id === id);
            if (index !== -1) {
                dispatch({ type: 'UPDATE_EXPERIENCE', payload: { index, field, value } });
            }
        }
    };

    const handleDeleteExperience = (id: string) => {
        if (dispatch) {
            dispatch({ type: 'DELETE_EXPERIENCE', payload: { id } });
        }
    };

  

    const experiences = data.experiences || [];


    // Don't render if no experiences and not editing
    if (!isEditing && experiences.length === 0) {
        return null;
    }

    return (
        <section id="experience" className="theme-creative-experience">
            <div className="theme-creative-experience-content">
                <div className="theme-creative-experience-header">
                    <h2 className="theme-creative-experience-title">Experience</h2>
                    <div className="theme-creative-experience-subtitle">
                        My professional journey and career highlights
                    </div>
                </div>

                <div className="theme-creative-experience-timeline">
                    {experiences.map((experience) => (
                        <ExperienceItem
                            key={experience.id}
                            experience={experience}
                            isEditing={isEditing}
                            onUpdate={handleUpdateExperience}
                            onDelete={handleDeleteExperience}
                        />
                    ))}

                    {isEditing && (
                        <div className="theme-creative-experience-add-container">
                            <button
                                onClick={handleAddExperience}
                                className="theme-creative-experience-add-btn"
                            >
                                <Plus className="theme-creative-experience-add-icon" />
                                Add Experience
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
